import { NextRequest, NextResponse } from "next/server";

export async function POST(request: NextRequest) {
  try {
    const { url } = await request.json();

    if (!url) {
      return NextResponse.json(
        { error: "URL é obrigatória" },
        { status: 400 }
      );
    }

    // Redirecionar para a nova API de extração
    const extractResponse = await fetch(`${request.nextUrl.origin}/api/opengraph/extract?url=${encodeURIComponent(url)}`);
    const data = await extractResponse.json();

    if (!extractResponse.ok) {
      return NextResponse.json(
        { error: data.error || "Falha ao extrair dados do Open Graph" },
        { status: extractResponse.status }
      );
    }

    return NextResponse.json(data);
  } catch (error) {
    console.error("Error extracting Open Graph data:", error);
    return NextResponse.json(
      { error: "Falha ao extrair dados do Open Graph" },
      { status: 500 }
    );
  }
}
