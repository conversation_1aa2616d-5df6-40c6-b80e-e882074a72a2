"use client";

import Image from "next/image";
import React from "react";
import userData<PERSON>son from "~/data/user.json";
import type { UserData } from "~/types/user";
import {
  getSocialIcon,
  type SocialPlatform,
  type IconSize as UtilIconSize,
} from "~/utils/socialIcons";
import { Button } from "~/components/ui/button";
import { ThemeToggle } from "~/components/ui/ThemeToggle";
import { FaMapMarkerAlt } from "react-icons/fa";
import { motion } from "framer-motion";

// Cast the imported JSON to the UserData type
const userData: UserData = userDataJson as UserData;

// Animation variants
const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1,
      delayChildren: 0.2,
      duration: 0.5,
    },
  },
};

const itemVariants = {
  hidden: { y: 20, opacity: 0 },
  visible: {
    y: 0,
    opacity: 1,
    transition: { duration: 0.4 },
  },
};

const socialIconVariants = {
  hover: {
    scale: 1.1,
    rotate: 5,
    transition: { duration: 0.2 },
  },
  tap: { scale: 0.95 },
};

// Helper function to render social icons with custom size
const renderSocialIcon = (
  platform: SocialPlatform,
  size: UtilIconSize = "lg",
  className?: string,
) => {
  return getSocialIcon(platform, size, className ?? "");
};

// Border radius variants for button styling
const borderRadiusVariants = {
  none: "rounded-none",
  sm: "rounded-sm",
  md: "rounded-md",
  lg: "rounded-lg",
  xl: "rounded-xl",
  "2xl": "rounded-2xl",
  "3xl": "rounded-3xl",
  full: "rounded-full",
} as const;

type BorderRadius = keyof typeof borderRadiusVariants;

// Props interface for customization
interface ProfileSidebarClientProps {
  /** Size of social media icons */
  iconSize?: UtilIconSize;
  /** Custom button size for social icons */
  buttonSize?: "sm" | "md" | "lg";
  /** Border radius for social icon buttons */
  borderRadius?: BorderRadius;
  /** Show enhanced hover effects */
  enhancedEffects?: boolean;
}

const ProfileSidebarClient: React.FC<ProfileSidebarClientProps> = ({
  iconSize = "xl",
  buttonSize = "md",
  borderRadius = "full",
  enhancedEffects = true,
}) => {
  const { profile } = userData;
  const hasLocation =
    profile.location &&
    profile.location.trim() !== "" &&
    profile.location !== "undefined";

  // Dynamic button sizing based on props
  const buttonSizeClasses = {
    sm: "h-10 w-10 p-2",
    md: "h-11 w-11 p-2.5",
    lg: "h-12 w-12 p-2",
  };

  // Enhanced hover effects
  const hoverEffects = enhancedEffects
    ? "hover:scale-105 hover:shadow-lg transition-all duration-200"
    : "transition-colors duration-200";

  // Get border radius class
  const borderRadiusClass = borderRadiusVariants[borderRadius];

  return (
    <div className="w-full py-8 md:flex md:min-h-screen md:items-center md:justify-center">
      <motion.div
        className="relative m-6 flex max-w-md flex-col items-center justify-center overflow-hidden rounded-3xl bg-white px-6 py-10 shadow-none sm:px-8 md:m-0 md:px-12 md:py-16 lg:max-w-lg lg:px-16 dark:bg-gray-900"
        initial="hidden"
        animate="visible"
        variants={containerVariants}
      >
        {/* Background gradient accent */}
        <div className="absolute top-0 right-0 left-0 h-32 rounded-t-3xl bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-950/30 dark:to-purple-950/30" />

        <div className="z-10 mx-auto flex w-full max-w-md flex-col items-center md:mx-auto md:max-w-md md:items-center lg:max-w-lg">
          {/* Avatar with animation */}
          <motion.div
            className="relative"
            variants={itemVariants}
            whileHover={{ scale: 1.05 }}
            transition={{ duration: 0.3 }}
          >
            <div className="h-32 w-32 rounded-full bg-gradient-to-r from-blue-100 to-purple-100 p-1 dark:from-blue-900/50 dark:to-purple-900/50">
              <Image
                src={profile.avatarUrl}
                alt={`${profile.name} avatar`}
                className="h-full w-full rounded-full object-cover"
                width={128}
                height={128}
                draggable={false}
                priority
              />
            </div>
            <div className="absolute -inset-1 -z-10 rounded-full bg-gradient-to-r from-blue-200/20 to-purple-200/20 blur-md dark:from-blue-500/10 dark:to-purple-500/10" />
          </motion.div>

          {/* Name and username */}
          <motion.h1
            className="mt-6 mb-2 text-center text-3xl font-bold text-gray-800 dark:text-white"
            variants={itemVariants}
          >
            {profile.name}
          </motion.h1>

          <motion.div
            className="mb-2 flex items-center justify-center gap-2 text-gray-600 dark:text-gray-300"
            variants={itemVariants}
          >
            <span className="text-lg font-medium">{profile.userName}</span>
          </motion.div>

          {/* Location if available */}
          {hasLocation && (
            <motion.div
              className="mb-4 flex items-center justify-center gap-1.5 text-gray-500 dark:text-gray-400"
              variants={itemVariants}
            >
              <FaMapMarkerAlt className="h-4 w-4 text-gray-400 dark:text-gray-500" />
              <span className="text-sm">{profile.location}</span>
            </motion.div>
          )}

          {/* Bio with better styling */}
          <motion.div className="mb-8 w-full" variants={itemVariants}>
            <p className="text-center leading-relaxed text-gray-600 dark:text-gray-300">
              {profile.bio}
            </p>
          </motion.div>

          {/* Social links with improved styling */}
          <motion.div className="mb-6 w-full" variants={itemVariants}>
            <div className="flex flex-wrap justify-center gap-3">
              {profile.socialLinks.map((link) => (
                <motion.div
                  key={link.platform}
                  variants={socialIconVariants}
                  whileHover="hover"
                  whileTap="tap"
                >
                  <Button
                    variant="outline"
                    className={`${buttonSizeClasses[buttonSize]} ${borderRadiusClass} ${hoverEffects}`}
                    onClick={() =>
                      window.open(link.url, "_blank", "noopener noreferrer")
                    }
                    aria-label={`Visit ${link.label}`}
                  >
                    <span className="sr-only">{link.label}</span>
                    {renderSocialIcon(
                      link.platform,
                      iconSize,
                      "transition-colors duration-200",
                    ) ?? (
                      <span className="text-xl font-semibold">
                        {link.label.charAt(0)}
                      </span>
                    )}
                  </Button>
                </motion.div>
              ))}
            </div>
          </motion.div>

          {/* Footer with improved styling */}
          <motion.footer
            className="mt-auto w-full border-t border-gray-100 pt-6 text-center text-sm text-gray-500 dark:border-gray-800 dark:text-gray-400"
            variants={itemVariants}
          >
            <p>
              Create your own directory with{" "}
              <a
                href="https://mydir.space"
                target="_blank"
                rel="noopener noreferrer"
                className="font-medium text-blue-600 transition-colors hover:text-blue-700 dark:text-blue-400 dark:hover:text-blue-300"
              >
                MyDir
              </a>
            </p>
            {/* Theme Toggle - Centered horizontally */}
            <div className="mt-10 flex justify-center">
              <ThemeToggle />
            </div>
          </motion.footer>
        </div>
      </motion.div>
    </div>
  );
};

export default ProfileSidebarClient;
