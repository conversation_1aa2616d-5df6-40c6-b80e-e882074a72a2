{"profile": {"name": "Awesome Vibe Coding", "userName": "@mydir.space", "description": "MyDir is a platform that helps you create and share your own personal directory.", "avatarUrl": "/images/logo/vibe-coding.svg", "location": "Brazil", "bio": "MyDir is a platform that helps you create and share your own personal directory.", "newsletterSubscription": {"placeholder": "Your email...", "buttonText": "Subscribe", "show": false, "webhookUrl": "https://webhook.site/your-test-id"}, "socialLinks": [{"platform": "reddit", "url": "#", "label": "Reddit"}, {"platform": "whatsapp", "url": "mailto:<EMAIL>", "label": "Email"}, {"platform": "instagram", "url": "#", "label": "Instagram"}, {"platform": "linkedin", "url": "#", "label": "LinkedIn"}, {"platform": "github", "url": "#", "label": "GitHub"}], "youtube": {"featuredVideoId": "TLXmBFo6KrE", "youtubeChannel": "@Fireship", "show": false}}, "directory": {"categories": [{"id": "all", "name": "All"}, {"id": "projects", "name": "Projects"}, {"id": "tools", "name": "Services & Tools"}, {"id": "ide-extensions", "name": "IDE Extensions"}, {"id": "yt-videos", "name": "Videos"}, {"id": "yt-channels", "name": "Channels"}, {"id": "articles", "name": "Articles"}, {"id": "github", "name": "GitHub Repos"}], "cards": {"projects": [{"type": "project", "id": "feedmap", "category": "projects", "title": "FeedMap", "subtitle": "Map your Feedback, Feed your Roadmap", "price": "$50.2k/mo", "chartColor": "bg-yellow-200", "chartStroke": "#FBBF24", "chartFill": "#FEF3C7", "chartPoints": "0,50 40,40 80,20 120,40 160,30 200,50", "url": "#"}, {"id": "github-sindresorhusawesome-awesome-lists-about-all-kinds-of-interesting-topics-mblfw52s", "type": "project", "category": "projects", "title": "GitHub - sindresorhus/awesome: 😎 Awesome lists about all kinds of interesting topics", "subtitle": "😎 Awesome lists about all kinds of interesting topics - sindresorhus/awesome", "price": "Free", "chartColor": "bg-blue-200", "chartStroke": "#3B82F6", "chartFill": "#DBEAFE", "chartPoints": "0,50 40,40 80,30 120,20 160,10 200,20", "url": "https://github.com/sindresorhus/awesome", "iconEmoji": "🚀"}], "tools": [{"type": "app", "category": "tools", "id": "notion", "title": "Notion", "subtitle": "All-in-one workspace", "description": "Notion is an all-in-one workspace for note-taking, task management, wikis, and collaboration.", "price": {"free": true, "cuponCode": "AWESOME10"}, "tags": ["note-taking", "productivity", "collaboration", "organization", "workflow"], "platforms": ["web", "iOS", "Android", "Windows", "macOS"], "logo": "https://upload.wikimedia.org/wikipedia/commons/4/45/Notion_app_logo.png", "image": "https://www.notion.so/images/meta/default.png", "url": "https://www.notion.so", "developer": "Notion Labs, Inc.", "updated_at": "2025-06-02"}, {"id": "chatgpt-mbfnwt5o", "type": "app", "category": "tools", "title": "ChatGPT", "subtitle": "A conversational AI system that listens, learns, and challenges", "price": {"free": false, "cuponCode": "TESTE"}, "logo": "https://cdn.oaistatic.com/assets/favicon-miwirzcw.ico", "image": "https://cdn.oaistatic.com/assets/chatgpt-share-og-u7j5uyao.webp", "url": "https://chatgpt.com", "description": "A conversational AI system that listens, learns, and challenges", "developer": "Unknown <PERSON><PERSON><PERSON>", "tags": ["productivity", "ai", "chatgpt", "openai"], "platforms": ["web"], "updated_at": "2025-06-02"}, {"id": "streamlit-a-faster-way-to-build-and-share-data-apps-mbl8cl1p", "type": "app", "category": "tools", "title": "Streamlit • A faster way to build and share data apps", "subtitle": "Streamlit is an open-source Python framework for data scientists and AI/ML engineers to deliver interactive data apps – in only a few lines of code.", "description": "Streamlit is an open-source Python framework for data scientists and AI/ML engineers to deliver interactive data apps – in only a few lines of code.", "price": {"free": true}, "tags": ["web", "tool"], "platforms": ["web"], "logo": "https://streamlit.io/favicon.svg", "image": "https://streamlit.io/images/uploads/sharing-image-facebook.jpg", "url": "https://streamlit.io/", "developer": "Unknown <PERSON><PERSON><PERSON>", "updated_at": "2025-06-06"}], "ide-extensions": [{"id": "augment-code-developer-ai-for-real-work-mbdzdktl", "type": "app", "category": "apps", "title": "Augment Code – Developer AI for real work", "subtitle": "Experience the AI platform that truly understands your codebase. Our developer AI helps teams code faster, make smarter decisions, and unlock collective knowledge. Try free today.", "price": "Free", "logo": "https://cdn.prod.website-files.com/66d76c2202b335e39ad2b5e8/66f302d663108ca67c19ddbc_Favicon.png", "image": "https://cdn.prod.website-files.com/66d76c2202b335e39ad2b5e8/675cb87f1ba91008af1bb69e_augment-open-graph.png", "badgeColor": "bg-blue-100 text-blue-700", "url": "https://www.augmentcode.com/", "detailsUrl": "/app/augment-code-developer-ai-for-real-work"}, {"id": "zencoder-the-ai-coding-agent-mbdzj63r", "type": "app", "category": "apps", "title": "Zencoder – The AI Coding Agent", "subtitle": "Transform your coding experience with <PERSON><PERSON><PERSON>, the next-gen AI agent that automates tasks, enhances collaboration, and improves code quality seamlessly within your existing workflow.", "price": "Free", "logo": "https://zencoder.ai/hubfs/export.png", "image": "https://zencoder.ai/hubfs/zencoder-meta-image.png", "badgeColor": "bg-blue-100 text-blue-700", "url": "https://zencoder.ai", "detailsUrl": "/app/zencoder-the-ai-coding-agent"}], "yt-videos": [{"id": "7j_NE6Pjv-E", "type": "video", "category": "yt-videos", "title": "Model Context Protocol (MCP), clearly explained (why it matters)", "description": "I’m joined by <PERSON><PERSON> to explain Model Context Protocol (MCP). <PERSON><PERSON> breaks down how MCPs essentially standardize  how LLMs connect with external tools ...", "thumbnail": "https://i.ytimg.com/vi/7j_NE6Pjv-E/hqdefault.jpg", "channelAvatar": "https://yt3.ggpht.com/5wiiTxMamM0NnCmGOt0iJ6eoVRmFLNIGF-BEiTZ_AVqaS02YIxAsB-2XY6xwLCUWQfoENG1MHKo=s800-c-k-c0x00ffffff-no-rj", "duration": "20:18", "channelTitle": "<PERSON>", "channelId": "UCPjNBjflYl0-HQtUvOx0Ibw", "viewCount": "744.7K", "publishedAt": "2 months ago", "platform": "youtube", "url": "https://www.youtube.com/watch?v=7j_NE6Pjv-E"}, {"id": "UtkPb9UevBM", "type": "video", "category": "yt-videos", "title": "Build Apps with Cursor like the 1% Using this New Method", "description": "🔹 Grab all templates and presentations— plus join a community of over 1000+ business owners and entrepreneurs on the same mission as you: https://www...", "thumbnail": "https://i.ytimg.com/vi/UtkPb9UevBM/hqdefault.jpg", "channelAvatar": "https://yt3.ggpht.com/0qsP0XfZ9ngKtpkGE45-m4-Ec4NyWk2T7dViLS6QXKu6U14HyLI2l95Abn07X921KuGZGmFi=s800-c-k-c0x00ffffff-no-rj", "duration": "25:09", "channelTitle": "<PERSON>", "channelId": "UCZonap26CSIMtVKesNQN4dw", "viewCount": "30.2K", "publishedAt": "1 weeks ago", "platform": "youtube", "url": "https://www.youtube.com/watch?v=UtkPb9UevBM"}, {"id": "YESvx24if2A", "type": "video", "category": "yt-videos", "title": "ISSO MUDA TUDO | Testando o novo agente de IA da OpenAI Codex", "description": "https://openai.com/index/introducing-codex/\n**Repo**: https://github.com/openai/codex\n\n⭐️ muito obrigado pela confiança em ser membro, espero que você...", "thumbnail": "https://i.ytimg.com/vi/YESvx24if2A/hqdefault.jpg", "channelAvatar": "", "duration": "10:23", "channelTitle": "<PERSON><PERSON><PERSON> | Dev", "channelId": "UCpKvMmsF6QrkVr_zWaLGK-A", "viewCount": "20.3K", "publishedAt": "2 days ago", "platform": "youtube", "url": "https://www.youtube.com/watch?v=YESvx24if2A"}, {"id": "CbVy0JdUreM", "type": "video", "category": "yt-videos", "title": "novo Codex é tipo um DEV Pleno com TDAH", "description": "Hey! Acabo de lançar https://perssua.com/ e se tudo der certo pretendo contratar até 500 estagiários se for possível, mas ainda estamos em versão beta...", "thumbnail": "https://i.ytimg.com/vi/CbVy0JdUreM/hqdefault.jpg", "channelAvatar": "", "duration": "32:53", "channelTitle": "<PERSON>", "channelId": "UCyHOBY6IDZF9zOKJPou2Rgg", "viewCount": "7.3K", "publishedAt": "Dec 6, 2024", "platform": "youtube", "url": "https://www.youtube.com/watch?v=CbVy0JdUreM"}, {"id": "Vv4kZDhT0ug", "type": "video", "category": "yt-videos", "title": "Do ZERO à APOSENTADORIA Só Com APP Porcaria", "description": "🔹 Quer aprender a criar apps do zero até a publicação comigo? Então dá uma olhada no meu curso: https://programadorbr.com/?sck=ytQrZACAP01\n\n🔹 Vem jo...", "thumbnail": "https://i.ytimg.com/vi/Vv4kZDhT0ug/hqdefault.jpg", "channelAvatar": "", "duration": "12:07", "channelTitle": "Programador BR", "channelId": "UCrdgeUeCll2QKmqmihIgKBQ", "viewCount": "1.9K", "publishedAt": "Dec 6, 2024", "platform": "youtube", "url": "https://www.youtube.com/watch?v=Vv4kZDhT0ug"}, {"id": "I7F8sszXk0XZk", "type": "video", "category": "yt-videos", "title": "Flutter Desktop: Suporte Multi Window, Merged Threads e mais", "description": "Hoje vamos conhecer como será o suporte Multi Janelas do Flutter Desktop, tanto para Mac OS, Linux e Windows. O Flutter terá ...", "thumbnail": "https://i.ytimg.com/vi/I7F8zXk0XZk/hqdefault.jpg", "channelAvatar": "https://yt3.ggpht.com/2H7Neag81ewBX3fyoNgnv674dlFNYrsBd0bYOoYtO50fxsyW49yKmbyH-CxWuFcZMpmUVGbT1g=s800-c-k-c0x00ffffff-no-rj", "duration": "13:59", "channelTitle": "Prof. <PERSON>", "channelId": "UCNTVzV1InxHV-YR0fSajqPQ", "viewCount": "603", "publishedAt": "Dec 6, 2024", "platform": "youtube", "url": "https://www.youtube.com/watch?v=I7F8zXk0XZk"}, {"id": "I7F8zXk0XZk", "type": "video", "category": "yt-videos", "title": "Flutter Desktop: Suporte Multi Window, Merged Threads e mais", "description": "Hoje vamos conhecer como será o suporte Multi Janelas do Flutter Desktop, tanto para Mac OS, Linux e Windows. O Flutter terá ...", "thumbnail": "https://i.ytimg.com/vi/I7F8zXk0XZk/hqdefault.jpg", "channelAvatar": "https://yt3.ggpht.com/2H7Neag81ewBX3fyoNgnv674dlFNYrsBd0bYOoYtO50fxsyW49yKmbyH-CxWuFcZMpmUVGbT1g=s800-c-k-c0x00ffffff-no-rj", "duration": "13:59", "channelTitle": "Prof. <PERSON>", "channelId": "UCNTVzV1InxHV-YR0fSajqPQ", "viewCount": "603", "publishedAt": "Dec 6, 2024", "platform": "youtube", "url": "https://www.youtube.com/watch?v=I7F8zXk0XZk"}], "yt-channels": [{"id": "UCyHOBY6IDZF9zOKJPou2Rgg", "type": "channel", "category": "yt-channels", "channelTitle": "<PERSON>", "subtitle": "@lucasmontano", "description": "Escrevendo código desde 2002, atualmente trabalho como Lead Engineer no Disney+ e falo sobre o que quero no Youtube. Tu pode se tornar membro para ter acesso a \"aulas\" e lives exclusivas: https://www.youtube.com/channel/UCyHOBY6IDZF9zOKJPou2Rgg/join\n \n| 🧠 App para firmar conteúdo: http://memoapp.dev \n| 🍓 Tu não vai entender: https://stupidbutton.club\n| 📱 Vendedor de Curso: https://appacademy.dev\n| 💜 Comunidade no Discord: https://discord.gg/cK8nSccsgD\n\nDa uma olhada no meu site pra mais links, lá tu encontra tudo que precisa saber sobre o que tenho feito e onde estou:\nhttp://lucasmontano.com/\n", "channelAvatar": "https://yt3.ggpht.com/vvXy8MERUMgDfpGyEtyld-gmLE9kBF2vh4ZlTR9vmN4SjT7RBaDK6I_lzhyksOj-2qwQRSmd=s800-c-k-c0x00ffffff-no-rj", "bannerImage": "https://yt3.googleusercontent.com/HblaO5F-x4bvS3yghIVJqeXe36ONI-S_M5vHqtlmTdw_9VpUmoc5dCVtpUMaA7_UNo5rHJzI", "subscriberCount": "358000", "videoCount": "1001", "viewCount": "34636231", "publishedAt": "Oct 2, 2011", "country": "BR", "platform": "youtube", "url": "https://www.youtube.com/channel/UCyHOBY6IDZF9zOKJPou2Rgg"}, {"id": "UCpKvMmsF6QrkVr_zWaLGK-A", "type": "channel", "category": "yt-channels", "channelTitle": "<PERSON><PERSON><PERSON> | Dev", "subtitle": "@kipperdev", "description": "Conteúdos sobre tecnologia, programação e dicas de carreira! - e as vezes umas piadas sem graça ;)\n", "channelAvatar": "https://yt3.ggpht.com/yKEHzyY0ji7NEwlIrCjByCkzbhLDvZZY5ElSixb9g3Vhk2nFqKjjKsZ9BiwYPbOh_M_tZGjx=s800-c-k-c0x00ffffff-no-rj", "bannerImage": "https://yt3.googleusercontent.com/zrzxWCObJXkoGihqoCVumdEO0UKhspPGpFbgGgdagqqblc_NWhBTYwXqI5PPqy6ScgpxkGc2Kw", "subscriberCount": "153000", "videoCount": "287", "viewCount": "7534930", "publishedAt": "24/07/2012", "country": "BR", "platform": "youtube", "url": "https://www.youtube.com/channel/UCpKvMmsF6QrkVr_zWaLGK-A"}, {"id": "UCX6OQ3DkcsbYNE6H8uQQuVA", "type": "channel", "category": "yt-channels", "channelTitle": "Mr<PERSON><PERSON><PERSON>", "subtitle": "@mrbeast", "description": "SUBSCRIBE FOR A COOKIE!\nNew MrBeast or MrBeast Gaming video every single Saturday at noon eastern time!\nAccomplishments:\n- Raised $20,000,000 To Plant 20,000,000 Trees\n- Removed 30,000,000 pounds of trash from the ocean\n- Helped 2,000 people walk again\n- Helped 1,000 blind people see\n- Helped 1,000 deaf people hear\n- Built wells in Africa\n- Built and gave away 100 houses\n- Adopted every dog in a shelter (twice)\n- Given millions to charity\n- Started my own snack company Feastables\n- Started my own software company Viewstats\n- Started Lunchly, a tasty, better-for-you lunch option\n- Gave away a private island (twice)\n- Gave away 1 million meals\n- I counted to 100k\n- Ran a marathon in the world's largest shoes\n- Survived 50 hours in Antarctica\n- Recreated Squid Game in real life\n- Created the largest competition show with 1000 people (Beast Games)\n- Gave $5,000,000 to one person\n- Passed T-Series to become most subscribed YouTube channel 🥹\nyou get it, I appreciate all of you so much :)\n", "channelAvatar": "https://yt3.ggpht.com/nxYrc_1_2f77DoBadyxMTmv7ZpRZapHR5jbuYe7PlPd5cIRJxtNNEYyOC0ZsxaDyJJzXrnJiuDE=s800-c-k-c0x00ffffff-no-rj", "bannerImage": "https://yt3.googleusercontent.com/5KWiriZZ_KEoEdSMFTJKj2M6vR_XSiRZeQ-ix0cvG3TGZuGoi8sfAjrSiZAP0GzXBkmF8ZGytw", "subscriberCount": "*********", "videoCount": "877", "viewCount": "85230595144", "publishedAt": "19/02/2012", "country": "US", "platform": "youtube", "url": "https://www.youtube.com/channel/UCX6OQ3DkcsbYNE6H8uQQuVA"}, {"id": "UCrdgeUeCll2QKmqmihIgKBQ", "type": "channel", "category": "yt-channels", "channelTitle": "Programador BR", "subtitle": "@programadorbr", "description": "O lugar certo para quem quer saber sobre programação, carreira e empreendedorismo. Não necessariamente nesta ordem.", "channelAvatar": "https://yt3.ggpht.com/ytc/AIdro_mH1r5SSCuZK0N9XqtwaIfQHih5mt2N4GlV1VHGweMPSew=s800-c-k-c0x00ffffff-no-rj", "bannerImage": "https://yt3.googleusercontent.com/gS6QAvaY_BV39JFzwHpID13cIRNpF2Nq0zdh69YuC_9viTAzh0xTJzbYhQYAJe7gyNy0VFLt3n4", "subscriberCount": "242000", "videoCount": "560", "viewCount": "11372611", "publishedAt": "Jul 19, 2016", "country": "BR", "platform": "youtube", "url": "https://www.youtube.com/channel/UCrdgeUeCll2QKmqmihIgKBQ"}, {"id": "UCNTVzV1InxHV-YR0fSajqPQ", "type": "channel", "category": "yt-channels", "channelTitle": "Supabase", "subtitle": "@supabase", "description": "Supabase is the best open source Firebase alternative. Create a backend in less than 2 minutes. Start your project with a Postgres Database, Authentication, instant APIs, & realtime subscriptions. \n\nWhether you're new to development or looking to migrate existing workloads, you'll find Supabase tutorials & tips that will make your app development easier. From database migrations to query optimizations, we cover everything you need to build quickly with Supabase.\n\nSupabase provides a full Postgres database for every project with pgvector, database backups, extensions, & more. Add & manage email & password, passwordless, OAuth, & mobile logins to your project through a suite of identity providers & APIs.\n\nKickstart your next project with templates & use our Instant APIs that do the hard work for you. We introspect your database to provide APIs instantly. No more repetitive CRUD endpoints -  build your app without ever leaving the dashboard!\n\nBuild in a weekend, scale to millions.\n", "channelAvatar": "https://yt3.ggpht.com/KVjptxDSWT7rjVfGax2TgTNVAYgplgo1z_fwaV3MFjPpcmNVZC0TIgQV030BPJ0ybCP3_Fz-2w=s800-c-k-c0x00ffffff-no-rj", "bannerImage": "https://yt3.googleusercontent.com/t1sfWDnt3FJK_IQRwoOFM7JQT0OTS3zKI0JVAiefOTSxdQ2xDdDckCQA7NCAvldRadPidJQttQ", "subscriberCount": "55400", "videoCount": "431", "viewCount": "4289882", "publishedAt": "Sep 6, 2020", "country": "US", "platform": "youtube", "url": "https://www.youtube.com/channel/UCNTVzV1InxHV-YR0fSajqPQ"}, {"id": "UCBJycsmduvYEL83R_U4JriQ", "type": "channel", "category": "yt-channels", "channelTitle": "Fireship", "subtitle": "@fireship", "description": "High-intensity ⚡ code tutorials and tech news to help you ship your app faster. New videos every week covering the topics every programmer should know. \n\nThe original home of #100SecondsOfCode #TheCodeReport and #CodeThisNotThat. Created by <PERSON>. \n\nBuilding an app? Get project support, advanced full courses, and more at https://fireship.io", "channelAvatar": "https://yt3.ggpht.com/ytc/AIdro_mKzklyPPhghBJQH5H3HpZ108YcE618DBRLAvRUD1AjKNw=s800-c-k-c0x00ffffff-no-rj", "bannerImage": "https://yt3.googleusercontent.com/62Kw34f1ysmycFceeNIFGsWpRDyqgDUSn2mAn29gwv7axMjN4NUVkJWqwEi4XKBE0016l7C4", "subscriberCount": "3890000", "videoCount": "737", "viewCount": "601712479", "publishedAt": "Mar 21, 2013", "country": "US", "platform": "youtube", "url": "https://www.youtube.com/channel/UCsBjURrPoezykLs9EqgamOA"}, {"id": "UCcefcZRL2oaA_uBNeo5UOWg", "type": "channel", "category": "yt-channels", "channelTitle": "Y Combinator", "subtitle": "@ycombinator", "description": "All the world is changing around technology and you may contribute a line of code. What will yours be? \n\nSubscribe for startup advice, founder stories, and a look inside Y Combinator.\n\nWhat is Y Combinator?\nWe invest $500,000 in every startup and work intensively with the founders for three months. For the life of their company, founders have access to the most powerful community in the world, essential advice, later-stage funding and programs, recruiting resources, and exclusive deals. \n\nVisit ycombinator.com to learn more.\n\n", "channelAvatar": "https://yt3.ggpht.com/dGyATx87Fp_s1nZvnupUFSnMqbAPZ6nqRby9Esk1m6YE41iBq-9Z8iGoIgHTCT9SiDBUpP2V=s800-c-k-c0x00ffffff-no-rj", "bannerImage": "https://yt3.googleusercontent.com/W8jHOZMEJaHirPbQJN9sf7wy519TqBinBlAYjDPdoZrvj1Ux5JQtNUrtVDUehzT4tGSHJ9eh", "subscriberCount": "1800000", "videoCount": "707", "viewCount": "78029225", "publishedAt": "Oct 24, 2013", "country": "US", "platform": "youtube", "url": "https://www.youtube.com/channel/UCcefcZRL2oaA_uBNeo5UOWg"}, {"id": "UC0m81bQuthaQZmFbXEY9QSw", "type": "channel", "category": "yt-channels", "channelTitle": "AICodeKing", "subtitle": "@aicodeking", "description": "Facing the future with AI! \n\nHere you'll find content about multiple AI Tools that are actually useful and sometimes free.\n\nFor Ads / Sponsorship : Comment on any of my videos and I'll reach back on how you can contact me.\n", "channelAvatar": "https://yt3.ggpht.com/nV61H0crb2n8eLYtWF2EkYksD6AVInPAtC6aNxWwX2dhTpnHWcTEgnEmmrNSvd0NMj-eChQu=s800-c-k-c0x00ffffff-no-rj", "bannerImage": "https://yt3.googleusercontent.com/oeJQh2rziRB7Cx65zVjrRP1uGgJUG21RnhhVMM647iHA5dBCMYH4w7JFEvNiuKxIsdN6XkDoT1M", "subscriberCount": "91100", "videoCount": "519", "viewCount": "7932421", "publishedAt": "Mar 27, 2024", "country": "US", "platform": "youtube", "url": "https://www.youtube.com/channel/UC0m81bQuthaQZmFbXEY9QSw"}, {"id": "UC-e2aGRMGMl67MDJoqcj19Q", "type": "channel", "category": "yt-channels", "channelTitle": "Eye on Tech", "subtitle": "@eyeontech", "description": "Eye on Tech focuses on the latest business technology and IT topics, including AI, cybersecurity, networking, cloud, storage, sustainability and more. Programmed strategically to inform the business tech buying cycle, Eye on Tech features insight and commentary from the writers, editors and experts from TechTarget's editorial department. ", "channelAvatar": "https://yt3.ggpht.com/ytc/AIdro_kuLA4-6F8uFEVOXpkR2ZqUKTy5_b7tPuVCiTq4UIfATA=s800-c-k-c0x00ffffff-no-rj", "bannerImage": "https://yt3.googleusercontent.com/nxC8sBGEhiit1fVSFpFW-Eyur5txNX23o7u4VZfcuv3_cPexfEKXZbJhip85GTGZqGIQoXCPcw", "subscriberCount": "116000", "videoCount": "1265", "viewCount": "21715560", "publishedAt": "Jun 6, 2006", "country": "US", "platform": "youtube", "url": "https://www.youtube.com/channel/UC-e2aGRMGMl67MDJoqcj19Q"}], "github": [{"id": "f/awesome-chatgpt-prompts", "type": "github", "category": "github", "avatar": "https://avatars.githubusercontent.com/u/196477?v=4", "repoUrl": "https://github.com/f/awesome-chatgpt-prompts", "title": "awesome-chatgpt-prompts", "subtitle": "This repo includes ChatGPT prompt curation to use ChatGPT and other LLM tools better.", "description": "This repo includes ChatGPT prompt curation to use ChatGPT and other LLM tools better.", "owner": "f", "repoName": "awesome-chatgpt-prompts", "language": "JavaScript", "stars": 128132, "forks": 16994, "issues": 274, "updatedAt": "2025-06-06T20:01:54Z"}, {"id": "sindresorhus/awesome", "type": "github", "category": "github", "avatar": "https://avatars.githubusercontent.com/u/170270?v=4", "repoUrl": "https://github.com/sindresorhus/awesome", "title": "awesome", "subtitle": "😎 Awesome lists about all kinds of interesting topics", "description": "😎 Awesome lists about all kinds of interesting topics", "owner": "sindresor<PERSON>", "repoName": "awesome", "language": "", "stars": 364037, "forks": 29251, "issues": 40, "updatedAt": "2025-06-06T19:42:28Z"}, {"type": "github", "category": "github", "id": "RooCodeInc/Roo-Code", "avatar": "https://github.githubassets.com/assets/GitHub-Mark-ea2971cee799.png", "repoUrl": "https://github.com/RooCodeInc/Roo-Code", "title": "Roo-<PERSON>", "subtitle": "Roo Code (prev. Roo Cline) gives you a whole dev team of AI agents in your code editor.", "description": "Roo Code (prev. Roo Cline) gives you a whole dev team of AI agents in your code editor.", "owner": "RooCodeInc", "repoName": "Roo-<PERSON>", "language": "TypeScript", "topics": [], "stars": 14884, "forks": 1541, "issues": 125, "updatedAt": "2025-06-03T02:17:18Z"}, {"type": "github", "category": "github", "id": "cline/cline", "avatar": "https://github.githubassets.com/assets/GitHub-Mark-ea2971cee799.png", "repoUrl": "https://github.com/cline/cline", "title": "Cline", "subtitle": "Autonomous coding agent right in your IDE, capable of creating/editing files, executing commands, using the browser, and more with your permission every step of the way.", "topics": [], "description": "Autonomous coding agent right in your IDE, capable of creating/editing files, executing commands, using the browser, and more with your permission every step of the way.", "owner": "Cline", "repoName": "Cline", "language": "TypeScript", "stars": 45010, "forks": 5458, "issues": 89, "updatedAt": "2025-06-03T02:32:23Z"}, {"type": "github", "category": "github", "id": "filipecalegario/awesome-vibe-coding", "avatar": "https://github.githubassets.com/assets/GitHub-Mark-ea2971cee799.png", "repoUrl": "https://github.com/vercel/next.js", "title": "awesome-vibe-coding", "subtitle": "A curated list of vibe coding references, collaborating with AI to write code.", "description": "A curated list of vibe coding references, collaborating with AI to write code.", "owner": "filipecalegario", "repoName": "awesome-vibe-coding", "language": "", "topics": ["ai-agent", "ai-coding", "awesome-list", "chatgpt", "copilot", "gpt", "llm", "llms", "vibe-coding"], "stars": 550, "forks": 61, "issues": 12, "updatedAt": "2025-03-13T17:46:56Z"}], "articles": [{"id": "nextjs-guide", "type": "article", "category": "articles", "title": "The Complete Guide to Next.js", "subtitle": "Everything you need to know about Next.js to build modern web applications", "price": "Free", "coverImage": "https://images.unsplash.com/photo-1555066931-4365d14bab8c?q=80&w=2070&auto=format&fit=crop", "excerpt": "Next.js has emerged as one of the most popular React frameworks for building modern web applications. In this comprehensive guide, we'll explore everything from basic setup to advanced features like server-side rendering, static site generation, and API routes.", "author": "<PERSON>", "publishedAt": "May 15, 2023", "readTime": "12 min read", "platform": "medium", "url": "https://medium.com/example/nextjs-guide"}, {"id": "flutter-guide", "type": "article", "category": "articles", "title": "The Complete Guide to Flutter", "subtitle": "Everything you need to know about Flutter to build modern mobile applications", "price": "Free", "coverImage": "https://images.unsplash.com/photo-1555066931-4365d14bab8c?q=80&w=2070&auto=format&fit=crop", "excerpt": "Flutter has emerged as one of the most popular frameworks for building modern mobile applications. In this comprehensive guide, we'll explore everything from basic setup to advanced features like server-side rendering, static site generation, and API routes.", "author": "<PERSON>", "publishedAt": "May 15, 2023", "readTime": "12 min read", "platform": "medium", "url": "https://medium.com/example/flutter-guide"}, {"id": "vite-guide", "type": "article", "category": "articles", "title": "The Complete Guide to Vite", "subtitle": "Everything you need to know about Vite to build modern web applications", "price": "Free", "coverImage": "https://images.unsplash.com/photo-1555066931-4365d14bab8c?q=80&w=2070&auto=format&fit=crop", "excerpt": "Vite has emerged as one of the most popular frameworks for building modern web applications. In this comprehensive guide, we'll explore everything from basic setup to advanced features like server-side rendering, static site generation, and API routes.", "author": "<PERSON>", "publishedAt": "May 15, 2023", "readTime": "12 min read", "platform": "medium", "url": "https://medium.com/example/vite-guide"}]}}}