import type {
  OpenGraphData,
  OpenGraphFormData,
  JsonExportConfig,
  JsonGenerationResponse,
  OpenGraphJsonCard
} from "~/types/opengraph";

/**
 * Service for handling OpenGraph JSON operations
 */
export class OpenGraphJsonService {
  private static readonly API_BASE = "/api/opengraph";

  /**
   * Generates JSON card data from OpenGraph data
   * @param openGraphData - Extracted OpenGraph data
   * @param formData - Form data with user modifications
   * @param config - Export configuration
   * @returns Promise with generated JSON card data
   */
  static async generateJson(
    openGraphData: OpenGraphData,
    formData: OpenGraphFormData,
    config: JsonExportConfig
  ): Promise<JsonGenerationResponse> {
    try {
      const response = await fetch(`${this.API_BASE}/generate-json`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          openGraphData,
          formData,
          config,
          downloadFormat: false
        }),
      });

      const data: JsonGenerationResponse = await response.json();

      if (!response.ok) {
        throw new Error(data.error ?? "Failed to generate JSON");
      }

      return data;
    } catch (error) {
      console.error("Error generating JSON:", error);
      return {
        success: false,
        error: error instanceof Error ? error.message : "Failed to generate JSON"
      };
    }
  }

  /**
   * Downloads JSON file
   * @param openGraphData - Extracted OpenGraph data
   * @param formData - Form data with user modifications
   * @param config - Export configuration
   * @returns Promise with blob for download
   */
  static async downloadJson(
    openGraphData: OpenGraphData,
    formData: OpenGraphFormData,
    config: JsonExportConfig
  ): Promise<{ success: boolean; blob?: Blob; filename?: string; error?: string }> {
    try {
      const response = await fetch(`${this.API_BASE}/generate-json`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          openGraphData,
          formData,
          config,
          downloadFormat: true
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to download JSON");
      }

      const blob = await response.blob();
      const filename = this.generateFilename(formData.title || formData.url, config.cardType);

      return {
        success: true,
        blob,
        filename
      };
    } catch (error) {
      console.error("Error downloading JSON:", error);
      return {
        success: false,
        error: error instanceof Error ? error.message : "Failed to download JSON"
      };
    }
  }

  /**
   * Triggers browser download of JSON file
   * @param openGraphData - Extracted OpenGraph data
   * @param formData - Form data with user modifications
   * @param config - Export configuration
   */
  static async triggerDownload(
    openGraphData: OpenGraphData,
    formData: OpenGraphFormData,
    config: JsonExportConfig
  ): Promise<void> {
    const result = await this.downloadJson(openGraphData, formData, config);

    if (!result.success || !result.blob || !result.filename) {
      throw new Error(result.error ?? "Failed to prepare download");
    }

    // Create download link and trigger download
    const url = URL.createObjectURL(result.blob);
    const link = document.createElement("a");
    link.href = url;
    link.download = result.filename;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  }

  /**
   * Copies JSON to clipboard
   * @param jsonCard - JSON card data to copy
   * @returns Promise indicating success
   */
  static async copyToClipboard(jsonCard: OpenGraphJsonCard): Promise<boolean> {
    try {
      const jsonString = JSON.stringify(jsonCard, null, 2);
      await navigator.clipboard.writeText(jsonString);
      return true;
    } catch (error) {
      console.error("Error copying to clipboard:", error);
      return false;
    }
  }

  /**
   * Validates export configuration
   * @param config - Configuration to validate
   * @returns Validation result
   */
  static validateConfig(config: JsonExportConfig): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    if (!config.cardType || !["app", "project"].includes(config.cardType)) {
      errors.push("Card type must be one of: app, project");
    }

    if (!config.category || config.category.trim().length === 0) {
      errors.push("Category is required");
    }

    if (!config.price) {
      errors.push("Price is required");
    } else if (typeof config.price === "string" && config.price.trim().length === 0) {
      errors.push("Price cannot be empty");
    } else if (typeof config.price === "object" && typeof config.price.free !== "boolean") {
      errors.push("Price object must have a valid 'free' boolean property");
    }



    return {
      isValid: errors.length === 0,
      errors
    };
  }

  /**
   * Generates filename for download
   * @param title - Title or URL
   * @param cardType - Type of card
   * @returns Generated filename
   */
  private static generateFilename(title: string, cardType: string): string {
    const baseTitle = title || "opengraph-card";
    const cleanTitle = baseTitle
      .toLowerCase()
      .replace(/[^a-z0-9\s-]/g, "")
      .replace(/\s+/g, "-")
      .replace(/-+/g, "-")
      .replace(/^-|-$/g, "")
      .substring(0, 30);

    const timestamp = new Date().toISOString().slice(0, 10); // YYYY-MM-DD
    return `${cleanTitle}-${cardType}-${timestamp}.json`;
  }

  /**
   * Gets example configuration for a card type
   * @param cardType - Type of card
   * @returns Example configuration
   */
  static getExampleConfig(cardType: "app" | "project"): JsonExportConfig {
    const configs: Record<"app" | "project", JsonExportConfig> = {
      app: {
        cardType: "app" as const,
        category: "apps",
        price: {
          free: true,
          cuponCode: undefined
        },
        generateDetailsUrl: true,
        developer: "Unknown Developer",
        tags: ["productivity"],
        platforms: ["web"]
      },
      project: {
        cardType: "project" as const,
        category: "projects",
        price: "$0/mo",
        generateDetailsUrl: true
      }
    };

    return configs[cardType];
  }
}
